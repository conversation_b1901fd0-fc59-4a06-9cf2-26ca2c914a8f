using HutongGames.PlayMaker;
using HutongGames.PlayMakerEditor;
using UnityEngine;
using UnityEditor;
using Fish.PlayMaker;

[CustomActionEditor(typeof(TestAction))]
public class TestActionEditor : CustomActionEditor
{
    public override bool OnGUI()
    {
        TestAction action = (TestAction)target;
        
        EditorGUILayout.LabelField("子动作配置", EditorStyles.boldLabel);
        
                // 显示执行方式选择
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("顺序执行", GUILayout.Width(100));
        action.executeSequentially.Value = EditorGUILayout.Toggle(action.executeSequentially.Value);
        EditorGUILayout.EndHorizontal();
        EditorGUILayout.Space();

        // 显示数组大小控制
        int newSize = EditorGUILayout.IntField("子动作数量", action.subActions.Length);
        if (newSize != action.subActions.Length)
        {
            System.Array.Resize(ref action.subActions, newSize);
            for (int i = 0; i < action.subActions.Length; i++)
            {
                if (action.subActions[i] == null)
                {
                    action.subActions[i] = new PMGAction();
                }
            }
        }

        EditorGUILayout.Space();

        // 显示每个子动作的配置
        for (int i = 0; i < action.subActions.Length; i++)
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField($"子动作 {i + 1}", EditorStyles.boldLabel);
            
            if (action.subActions[i] == null)
            {
                action.subActions[i] = new PMGAction();
            }

            var subAction = action.subActions[i];

            // 动作类型选择
            subAction.actionType = (GActionUniversal.ActionType)EditorGUILayout.EnumPopup("动作类型", subAction.actionType);

            EditorGUILayout.Space();

            // 根据类型显示对应的属性
            switch (subAction.actionType)
            {
                case GActionUniversal.ActionType.MoveTo:
                    EditorGUILayout.LabelField("MoveTo 属性", EditorStyles.miniBoldLabel);
                    
                    // Duration字段
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("Duration", GUILayout.Width(80));
                    subAction.moveToAction.duration.Value = EditorGUILayout.FloatField(subAction.moveToAction.duration.Value);
                    EditorGUILayout.EndHorizontal();
                    
                    // Target字段
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("Target", GUILayout.Width(80));
                    subAction.moveToAction.target.Value = EditorGUILayout.Vector3Field("", subAction.moveToAction.target.Value);
                    EditorGUILayout.EndHorizontal();
                    break;

                case GActionUniversal.ActionType.FadeTo:
                    EditorGUILayout.LabelField("Fade 属性", EditorStyles.miniBoldLabel);
                    
                    // Duration字段
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("Duration", GUILayout.Width(80));
                    subAction.fadeAction.duration.Value = EditorGUILayout.FloatField(subAction.fadeAction.duration.Value);
                    EditorGUILayout.EndHorizontal();
                    
                    // Target字段
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("Target", GUILayout.Width(80));
                    subAction.fadeAction.target.Value = EditorGUILayout.FloatField(subAction.fadeAction.target.Value);
                    EditorGUILayout.EndHorizontal();
                    break;

                case GActionUniversal.ActionType.Hide:
                    EditorGUILayout.LabelField("Hide 属性", EditorStyles.miniBoldLabel);
                    EditorGUILayout.LabelField("(无需额外配置)", EditorStyles.miniLabel);
                    break;
            }

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();
        }

        return GUI.changed;
    }
} 