using UnityEditor;
using HutongGames.PlayMakerEditor;
using Fish.PlayMaker;
using UnityEngine;

namespace Fish.PlayMakerEditor
{
	[CustomActionEditor(typeof(GActionUniversal))]
	public class GActionUniversalFsmEditor : CustomActionEditor
	{

		public override bool OnGUI()
		{
			var action = target as GActionUniversal;
			EditField("targetObject");

			int newSize = EditorGUILayout.IntField("子动作数量", action.actions.Length);
			if (newSize != action.actions.Length)
			{
				System.Array.Resize(ref action.actions, newSize);
				for (int i = 0; i < action.actions.Length; i++)
				{
					if (action.actions[i] == null)
					{
						action.actions[i] = new PMGAction();
					}
				}
			}

			EditorGUILayout.Space();

			// 显示每个子动作的配置
			for (int i = 0; i < action.actions.Length; i++)
			{
				EditorGUILayout.BeginVertical("box");

				if (action.actions[i] == null)
				{
					action.actions[i] = new PMGAction();
				}

				var subAction = action.actions[i];

				// 添加折叠功能
				EditorGUILayout.BeginHorizontal();
				subAction.foldout = EditorGUILayout.Foldout(subAction.foldout, $"子动作 {i + 1} ({subAction.actionType})", true, EditorStyles.foldoutHeader);
				EditorGUILayout.EndHorizontal();

				if (subAction.foldout)
				{
					EditorGUI.indentLevel++;

					// 动作类型选择
					subAction.actionType = (GActionUniversal.ActionType)EditorGUILayout.EnumPopup("动作类型", subAction.actionType);

					EditorGUILayout.Space();

					// 使用递归方法显示子动作属性
					DrawSubActionProperties(subAction, 0);

					EditorGUI.indentLevel--;
				}

				EditorGUILayout.EndVertical();
				EditorGUILayout.Space();
			}

			return GUI.changed;
		}

		private void DrawSubActionProperties(PMGAction subAction, int depth)
		{
			// 防止无限递归，限制嵌套深度
			if (depth > 5)
			{
				EditorGUILayout.LabelField("嵌套深度过深，已停止显示", EditorStyles.miniLabel);
				return;
			}

			// 增加缩进级别
			EditorGUI.indentLevel += depth;

			// 根据类型显示对应的属性
			switch (subAction.actionType)
			{
				case GActionUniversal.ActionType.MoveTo:
					// MoveTo属性始终显示，不需要折叠（属性简单）
					EditorGUILayout.LabelField("MoveTo 属性", EditorStyles.miniBoldLabel);

					// Duration字段
					EditorGUILayout.BeginHorizontal();
					EditorGUILayout.LabelField("Duration", GUILayout.Width(80));
					subAction.moveToAction.duration.Value = EditorGUILayout.FloatField(subAction.moveToAction.duration.Value);
					EditorGUILayout.EndHorizontal();

					// Target字段
					EditorGUILayout.BeginHorizontal();
					EditorGUILayout.LabelField("Target", GUILayout.Width(80));
					subAction.moveToAction.target.Value = EditorGUILayout.Vector3Field("", subAction.moveToAction.target.Value);
					EditorGUILayout.EndHorizontal();
					break;

				case GActionUniversal.ActionType.FadeTo:
					// FadeTo属性始终显示，不需要折叠（属性简单）
					EditorGUILayout.LabelField("Fade 属性", EditorStyles.miniBoldLabel);

					// Duration字段
					EditorGUILayout.BeginHorizontal();
					EditorGUILayout.LabelField("Duration", GUILayout.Width(80));
					subAction.fadeAction.duration.Value = EditorGUILayout.FloatField(subAction.fadeAction.duration.Value);
					EditorGUILayout.EndHorizontal();

					// Target字段
					EditorGUILayout.BeginHorizontal();
					EditorGUILayout.LabelField("Target", GUILayout.Width(80));
					subAction.fadeAction.target.Value = EditorGUILayout.FloatField(subAction.fadeAction.target.Value);
					EditorGUILayout.EndHorizontal();
					break;

				case GActionUniversal.ActionType.Hide:
					// Hide属性始终显示，不需要折叠（无配置）
					EditorGUILayout.LabelField("Hide 属性", EditorStyles.miniBoldLabel);
					EditorGUILayout.LabelField("(无需额外配置)", EditorStyles.miniLabel);
					break;

				case GActionUniversal.ActionType.Sequence:
					// 添加Sequence的折叠功能
					subAction.sequenceAction.foldout = EditorGUILayout.Foldout(subAction.sequenceAction.foldout, "Sequence 属性", true, EditorStyles.foldoutHeader);
					
					if (subAction.sequenceAction.foldout)
					{
						EditorGUI.indentLevel++;

						// 显示数组大小控制
						int newSize = EditorGUILayout.IntField("子动作数量", subAction.sequenceAction.actions.Length);
						if (newSize != subAction.sequenceAction.actions.Length)
						{
							System.Array.Resize(ref subAction.sequenceAction.actions, newSize);
							for (int i = 0; i < subAction.sequenceAction.actions.Length; i++)
							{
								if (subAction.sequenceAction.actions[i] == null)
								{
									subAction.sequenceAction.actions[i] = new PMGAction();
								}
							}
						}

						EditorGUILayout.Space();

						// 递归显示每个子动作的配置
						for (int i = 0; i < subAction.sequenceAction.actions.Length; i++)
						{
							// 为嵌套的子动作创建更深的背景色
							var bgColor = depth % 2 == 0 ? "box" : "helpbox";
							EditorGUILayout.BeginVertical(bgColor);
							
							if (subAction.sequenceAction.actions[i] == null)
							{
								subAction.sequenceAction.actions[i] = new PMGAction();
							}

							var nestedSubAction = subAction.sequenceAction.actions[i];

							// 添加嵌套子动作的折叠功能
							EditorGUILayout.BeginHorizontal();
							nestedSubAction.foldout = EditorGUILayout.Foldout(nestedSubAction.foldout, $"序列子动作 {i + 1} (深度{depth + 1}) ({nestedSubAction.actionType})", true, EditorStyles.foldout);
							EditorGUILayout.EndHorizontal();

							if (nestedSubAction.foldout)
							{
								EditorGUI.indentLevel++;

								// 动作类型选择
								nestedSubAction.actionType = (GActionUniversal.ActionType)EditorGUILayout.EnumPopup("动作类型", nestedSubAction.actionType);

								EditorGUILayout.Space();

								// 递归显示属性
								DrawSubActionProperties(nestedSubAction, depth + 1);

								EditorGUI.indentLevel--;
							}
							
							EditorGUILayout.EndVertical();
							EditorGUILayout.Space();
						}

						EditorGUI.indentLevel--;
					}
					break;
			}

			// 恢复缩进级别
			EditorGUI.indentLevel -= depth;
		}
	}
}