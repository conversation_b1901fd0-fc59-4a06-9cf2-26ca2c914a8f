using System;
using HutongGames.PlayMaker;

namespace Fish.PlayMaker
{
	[ActionCategory("Fish/Common")]
	[HutongGames.PlayMaker.Tooltip("GAction实现")]
    public class GActionUniversal : FsmStateAction
    {
        [RequiredField] [HutongGames.PlayMaker.Tooltip("目标对象")]
        public FsmOwnerDefault targetObject;


		[ArrayEditor(VariableType.Unknown, "", 0, 0, 65536)]
		public PMGAction[] actions = new PMGAction[0];
		
		public override void Reset()
		{
			targetObject = null;
			actions = new PMGAction[0];
		}
        
        public enum ActionType
        {
            Show,
            Hide,
            RemoveSelf,
            FlipX,
            FlipY,
            CallFunc,//todo
            DelayTime,
            MoveBy,
            MoveTo,
            RotateBy,
            RotateTo,
            RotateBy3D,
            ScaleTo,
            ScaleBy,
            FadeTo,
            TintTo,
            BlendTo,
            Flash,
            BezierTo,
            CanvasGroupAlphaFadeTo,
			Sequence,
        }
    }

	[Serializable]
	public class PMGAction
	{
		public GActionUniversal.ActionType actionType;
		public PMGActionMoveTo moveToAction;
		public PMGActionFade fadeAction;
		public PMGActionHide hideAction;
		public PMGActionSequence sequenceAction;

		// 编辑器折叠状态（不序列化）
		[System.NonSerialized]
		public bool foldout = true;

		public PMGAction()
		{
			moveToAction = new PMGActionMoveTo();
			fadeAction = new PMGActionFade();
			hideAction = new PMGActionHide();
			sequenceAction = new PMGActionSequence();
		}
	}

	[Serializable]
	public class PMGActionSequence
	{
		[ArrayEditor(VariableType.Unknown, "", 0, 0, 65536)]
		public PMGAction[] actions = new PMGAction[0];

		// 编辑器折叠状态（不序列化）
		[System.NonSerialized]
		public bool foldout = true;
	}

	[Serializable]
	public class PMGActionMoveTo
	{
		[RequiredField]
		public FsmFloat duration;
		[RequiredField]
		public FsmVector3 target;

		public PMGActionMoveTo()
		{
			duration = new FsmFloat();
			target = new FsmVector3();
		}
	}

	[Serializable]
	public class PMGActionFade
	{
		[RequiredField]
		public FsmFloat duration;
		[RequiredField]
		public FsmFloat target;

		public PMGActionFade()
		{
			duration = new FsmFloat();
			target = new FsmFloat();
		}
	}

	[Serializable]
	public class PMGActionHide
	{
		// Hide动作暂时不需要额外参数
	}
}