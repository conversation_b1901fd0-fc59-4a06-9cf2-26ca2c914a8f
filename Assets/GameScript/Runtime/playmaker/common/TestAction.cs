using DG.Tweening;
using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Fish.PlayMaker
{
	[ActionCategory("Fish/Test")]
	public class TestAction : FsmStateAction
	{

		[ArrayEditor(VariableType.Unknown, "", 0, 0, 65536)]
		public SubAction[] subActions = new SubAction[0];

		[HutongGames.PlayMaker.Tooltip("是否顺序执行子动作")]
		public FsmBool executeSequentially = new FsmBool { Value = false };

		private int currentActionIndex = 0;
		private float actionStartTime;
		private bool isExecuting = false;

		public override void OnEnter()
		{
			// 验证子动作数组
			if (subActions == null || subActions.Length == 0)
			{
				LogWarning("没有配置子动作");
				Finish();
				return;
			}

			currentActionIndex = 0;
			isExecuting = true;

			if (executeSequentially.Value)
			{
				// 顺序执行
				StartSubAction(currentActionIndex);
			}
			else
			{
				// 并行执行所有子动作
				for (int i = 0; i < subActions.Length; i++)
				{
					ExecuteSubAction(subActions[i]);
				}
				Finish();
			}
		}

		public override void OnUpdate()
		{
			if (!isExecuting || !executeSequentially.Value)
				return;

			// 检查当前动作是否完成
			if (IsCurrentActionCompleted())
			{
				currentActionIndex++;
				if (currentActionIndex >= subActions.Length)
				{
					// 所有动作执行完毕
					isExecuting = false;
					Finish();
				}
				else
				{
					// 开始下一个动作
					StartSubAction(currentActionIndex);
				}
			}
		}

		private void StartSubAction(int index)
		{
			if (index >= 0 && index < subActions.Length)
			{
				actionStartTime = Time.time;
				ExecuteSubAction(subActions[index]);
			}
		}

		private bool IsCurrentActionCompleted()
		{
			if (currentActionIndex >= subActions.Length)
				return true;

			var subAction = subActions[currentActionIndex];
			float duration = GetActionDuration(subAction);

			return duration <= 0 || Time.time - actionStartTime >= duration;
		}

		private float GetActionDuration(SubAction subAction)
		{
			switch (subAction.actionType)
			{
				case GActionUniversal.ActionType.MoveTo:
					return subAction.moveToAction.duration.Value;
				case GActionUniversal.ActionType.FadeTo:
					return subAction.fadeAction.duration.Value;
				case GActionUniversal.ActionType.Hide:
					return 0f; // Hide动作立即完成
				default:
					return 0f;
			}
		}

		private void ExecuteSubAction(SubAction subAction)
		{
			switch (subAction.actionType)
			{
				case GActionUniversal.ActionType.MoveTo:
					ExecuteMoveTo(subAction.moveToAction);
					break;
				case GActionUniversal.ActionType.FadeTo:
					ExecuteFade(subAction.fadeAction);
					break;
				case GActionUniversal.ActionType.Hide:
					ExecuteHide(subAction.hideAction);
					break;
			}
		}

		private void ExecuteMoveTo(ActionMoveTo moveAction)
		{
			if (Owner != null)
			{
				Log($"执行MoveTo动作: 目标位置={moveAction.target.Value}, 持续时间={moveAction.duration.Value}");
				// 这里可以添加实际的移动逻辑
				// 例如使用DOTween或其他移动组件
				Owner.transform.DOMove(moveAction.target.Value, moveAction.duration.Value);
			}
		}

		private void ExecuteFade(ActionFade fadeAction)
		{
			if (Owner != null)
			{
				Log($"执行Fade动作: 目标透明度={fadeAction.target.Value}, 持续时间={fadeAction.duration.Value}");
				// 这里可以添加实际的淡入淡出逻辑
			}
		}

		private void ExecuteHide(ActionHide hideAction)
		{
			if (Owner != null)
			{
				Log("执行Hide动作");
				Owner.SetActive(false);
			}
		}
	}

	[Serializable]
	public class SubAction
	{
		public GActionUniversal.ActionType actionType;
		public ActionMoveTo moveToAction;
		public ActionFade fadeAction;
		public ActionHide hideAction;
		public ActionSequence sequenceAction;

		// 编辑器折叠状态（不序列化）
		[System.NonSerialized]
		public bool foldout = true;

		public SubAction()
		{
			moveToAction = new ActionMoveTo();
			fadeAction = new ActionFade();
			hideAction = new ActionHide();
			sequenceAction = new ActionSequence();
		}
	}

	[Serializable]
	public class ActionSequence
	{
		[ArrayEditor(VariableType.Unknown, "", 0, 0, 65536)]
		public SubAction[] actions = new SubAction[0];
	}

	[Serializable]
	public class ActionMoveTo
	{
		[RequiredField]
		public FsmFloat duration;
		[RequiredField]
		public FsmVector3 target;

		public ActionMoveTo()
		{
			duration = new FsmFloat();
			target = new FsmVector3();
		}
	}

	[Serializable]
	public class ActionFade
	{
		[RequiredField]
		public FsmFloat duration;
		[RequiredField]
		public FsmFloat target;

		public ActionFade()
		{
			duration = new FsmFloat();
			target = new FsmFloat();
		}
	}

	[Serializable]
	public class ActionHide
	{
		// Hide动作暂时不需要额外参数
	}
}